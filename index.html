<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Komrade Reviews - For the Glory of the User!</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Anton&family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <style>
        /* --- General Styles & Variables --- */
        :root {
            --main-red: #b71c1c; /* A strong, commanding red */
            --dark-green: #333d29; /* A deep, military green */
            --off-white: #f5f5dc; /* A parchment-like beige */
            --dark-gray: #212121;  /* For primary text */
            --light-gray: #bdbdbd; /* For subtle borders */
        }

        body {
            background-color: var(--dark-green);
            background-image: url('data:image/svg+xml,%3Csvg width="6" height="6" viewBox="0 0 6 6" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%232a3223" fill-opacity="0.4" fill-rule="evenodd"%3E%3Cpath d="M5 0h1L0 6V5zM6 5v1H5z"/%3E%3C/g%3E%3C/svg%3E'); /* Subtle pattern */
            color: var(--dark-gray);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 20px;
        }

        /* --- Typography --- */
        h1, h2, h3 {
            font-family: 'Anton', sans-serif;
            color: var(--main-red);
            text-transform: uppercase;
            letter-spacing: 1.5px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        /* --- Header --- */
        .header {
            background-color: rgba(0,0,0,0.2);
            padding: 10px 0;
            text-align: center;
            border-bottom: 4px solid var(--main-red);
        }

        .header img {
            max-height: 80px;
            height: auto;
            margin: 0;
            filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.5));
            transition: filter 0.3s ease;
        }

        .header img:hover {
            filter: drop-shadow(2px 2px 8px rgba(0,0,0,0.7)) brightness(1.1);
        }

        /* --- Hero Section --- */
        .hero {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 40px;
            padding: 50px 20px;
            background: var(--off-white);
            border-radius: 8px;
            margin-top: 30px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .hero-image {
            flex-basis: 350px;
            flex-shrink: 0;
        }

        .hero-image img {
            width: 100%;
            height: auto;
            border-radius: 8px;
            border: 5px solid var(--dark-gray);
            box-shadow: 5px 5px 15px rgba(0,0,0,0.4);
        }

        .hero-text {
            flex-basis: 60%;
        }

        .hero-text h2 {
            font-size: 3em;
            margin-top: 0;
        }

        .hero-text p {
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        /* --- Services Section --- */
        .services {
            padding: 50px 0;
            text-align: center;
            background: linear-gradient(135deg, var(--dark-green) 0%, #2a3223 50%, var(--dark-green) 100%);
            position: relative;
        }

        .services::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(183, 28, 28, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(183, 28, 28, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .services > * {
            position: relative;
            z-index: 1;
        }

        .services h2 {
            font-size: 2.8em;
            color: var(--off-white);
            margin-bottom: 20px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .service-card {
            position: relative;
            cursor: pointer;
            border-radius: 12px;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease, filter 0.3s ease;
            box-shadow:
                0 8px 25px rgba(0,0,0,0.3),
                0 0 0 2px rgba(183, 28, 28, 0.2),
                inset 0 1px 0 rgba(255,255,255,0.1);
            background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(0,0,0,0.1));
            backdrop-filter: blur(5px);
        }

        .service-card:hover {
            transform: scale(1.08) translateY(-15px);
            box-shadow:
                0 25px 50px rgba(0,0,0,0.5),
                0 0 0 3px var(--main-red),
                inset 0 1px 0 rgba(255,255,255,0.2);
            filter: brightness(1.1);
        }

        .service-card img {
            width: 100%;
            height: 300px;
            object-fit: cover;
            display: block;
            transition: filter 0.3s ease;
        }

        .service-card:hover img {
            filter: brightness(1.1) contrast(1.1);
        }

        .service-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(
                transparent 0%,
                rgba(0,0,0,0.4) 30%,
                rgba(0,0,0,0.8) 70%,
                rgba(0,0,0,0.95) 100%
            );
            color: var(--off-white);
            padding: 25px 20px;
            text-align: center;
            backdrop-filter: blur(2px);
        }

        .service-overlay h3 {
            font-size: 1.4em;
            margin: 0 0 8px 0;
            color: var(--off-white);
            text-shadow:
                2px 2px 4px rgba(0,0,0,0.9),
                0 0 10px rgba(183, 28, 28, 0.3);
            letter-spacing: 1px;
        }

        .service-overlay .price {
            font-family: 'Anton', sans-serif;
            font-size: 1.8em;
            color: var(--main-red);
            margin: 0;
            text-shadow:
                2px 2px 4px rgba(0,0,0,0.9),
                0 0 15px rgba(183, 28, 28, 0.5);
            filter: drop-shadow(0 0 8px rgba(183, 28, 28, 0.4));
        }

        .btn {
            display: inline-block;
            background-color: var(--main-red);
            color: var(--off-white);
            padding: 12px 25px;
            text-decoration: none;
            font-family: 'Anton', sans-serif;
            text-transform: uppercase;
            font-size: 1.2em;
            border-radius: 4px;
            border: 2px solid var(--main-red);
            transition: background-color 0.3s, color 0.3s;
        }

        .btn:hover {
            background-color: #c62828;
            color: #fff;
        }

        /* --- Modal Popup Styles --- */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            animation: fadeIn 0.3s ease;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: var(--off-white);
            padding: 30px;
            border-radius: 8px;
            border: 3px solid var(--main-red);
            max-width: 500px;
            width: 90%;
            position: relative;
            animation: slideIn 0.3s ease;
            box-shadow: 0 20px 40px rgba(0,0,0,0.5);
        }

        .modal-close {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 28px;
            font-weight: bold;
            color: var(--dark-gray);
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .modal-close:hover {
            color: var(--main-red);
        }

        .modal h3 {
            margin-top: 0;
            color: var(--main-red);
            font-size: 2em;
        }

        .modal .price {
            font-family: 'Anton', sans-serif;
            font-size: 2.5em;
            color: var(--main-red);
            margin: 15px 0;
            text-align: center;
        }

        .modal p {
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-secondary {
            background-color: var(--dark-gray);
            border-color: var(--dark-gray);
        }

        .btn-secondary:hover {
            background-color: #424242;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* --- Footer --- */
        .footer {
            text-align: center;
            padding: 20px;
            margin-top: 30px;
            color: var(--light-gray);
            background-color: rgba(0,0,0,0.2);
            border-top: 2px solid var(--dark-gray);
        }
        
        /* --- Responsive Design --- */
        @media (max-width: 768px) {
            .hero {
                flex-direction: column;
                text-align: center;
            }

            .hero-image {
                flex-basis: auto;
                width: 80%;
                max-width: 300px;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .modal-content {
                margin: 20px;
                padding: 20px;
            }

            .modal-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>

    <!-- Header -->
    <header class="header">
        <img src="src/header.png" alt="Komrade Reviews" />
    </header>

    <div class="container">
        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-image">
                <!-- User-provided image -->
                <img src="data:image/jpeg;base64,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-        I've included your image directly in the HTML. This makes the entire page self-contained.
    -->
                <img src="https://content.googleapis.com/v1/blobs/uploaded:Gemini_Generated_Image_hd0mg3hd0mg3hd0m%20(1).jpg-a72f4ece-89b3-44e5-a916-4ae1d336fd91" alt="Portrait of Komrade Reviewer" onerror="this.onerror=null;this.src='https://placehold.co/350x450/333d29/f5f5dc?text=Komrade';">
            </div>
            <div class="hero-text">
                <h2>YOUR WEBSITE UNDER THE MICROSCOPE OF PROGRESS!</h2>
                <p>
                    Komrade Reviews provides critical analysis of your digital presence. We identify weaknesses and inefficiencies to serve the digital collective more effectively. No capitalist distractions, only actionable insights.
                </p>
            </div>
        </section>

        <!-- Services Section -->
        <section class="services">
            <h2>OUR STRATEGIC OBJECTIVES</h2>
            <div class="services-grid">
                <!-- Service 1: Bronze Review -->
                <div class="service-card" onclick="openModal('bronze')">
                    <img src="src/bronze.png" alt="Bronze Service - Website Review" />
                    <div class="service-overlay">
                    </div>
                </div>

                <!-- Service 2: Silver Quick Fix -->
                <div class="service-card" onclick="openModal('silver')">
                    <img src="src/silver.png" alt="Silver Service - Quick Fix" />
                    <div class="service-overlay">
                    </div>
                </div>

                <!-- Service 3: Gold Full Revamp -->
                <div class="service-card" onclick="openModal('gold')">
                    <img src="src/gold.png" alt="Gold Service - Full Revamp" />
                    <div class="service-overlay">
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <p>© 2025 KOMRADE REVIEWS. FORWARD, FOR A BETTER WEB!</p>
    </footer>

    <!-- Modal Popup -->
    <div id="serviceModal" class="modal">
        <div class="modal-content">
            <span class="modal-close" onclick="closeModal()">&times;</span>
            <h3 id="modalTitle">Service Title</h3>
            <div class="price" id="modalPrice">$0</div>
            <p id="modalDescription">Service description goes here.</p>
            <div class="modal-buttons">
                <a href="#" class="btn" id="checkoutBtn">Check out</a>
                <a href="#" class="btn btn-secondary" id="actuallyBtn">Actually...</a>
            </div>
        </div>
    </div>

    <script>
        // Service data
        const services = {
            bronze: {
                title: "REVIEW",
                price: "$100",
                description: "A detailed analysis of your website's key elements. We will identify weaknesses and potential for growth with ruthless efficiency. Perfect for comrades seeking initial insights into their digital presence."
            },
            silver: {
                title: "QUICK FIX",
                price: "$1000",
                description: "Targeted solutions for specific issues. Minor code adjustments, content tweaks, and usability enhancements executed with precision. For comrades who know what needs fixing."
            },
            gold: {
                title: "FULLREVAMP",
                price: "$10000",
                description: "A complete overhaul. Modernization of design, architecture, and functionality to meet the highest standards of digital efficiency. The ultimate transformation for the digital revolution."
            }
        };

        function openModal(serviceType) {
            const modal = document.getElementById('serviceModal');
            const service = services[serviceType];

            document.getElementById('modalTitle').textContent = service.title;
            document.getElementById('modalPrice').textContent = service.price;
            document.getElementById('modalDescription').textContent = service.description;

            modal.classList.add('show');
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }

        function closeModal() {
            const modal = document.getElementById('serviceModal');
            modal.classList.remove('show');
            document.body.style.overflow = 'auto'; // Restore scrolling
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('serviceModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });
    </script>

</body>
</html>
